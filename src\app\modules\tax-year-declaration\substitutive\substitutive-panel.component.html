<se-panel
  [id]="'substitutive_panel_id'"
  [title]="
    'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.TITLE'
      | translate
  "
  [colapsible]="false"
  [collapsed]="false"
  [panelTheme]="'default'"
  [customActions]="downloadDocuments"
>
  <se-alert
    *ngIf="_substitutiveData?.hasDeclaracioPrevia"
    [title]="this.alertInfo"
    [type]="'info'"
  >
  </se-alert>
  <form [formGroup]="componentForm">
    <ng-container
      *ngIf="_substitutiveData?.samePresenter; else differentPresenter"
    >
      <se-switch
        [id]="'substitutiveSwitchId'"
        [label]="
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.SWITCH'
            | translate
        "
        formControlName="substitutiva"
        (onToggle)="onSubstitutiveChange($event)"
      >
      </se-switch>
      <se-table
        [columns]="tableColumns"
        [data]="tableData"
        [resizable]="true"
        [cellTemplatePriorityOrder]="'row-column-cell'"
        [currentPage]="0"
        [itemsPerPage]="5"
        [showPagination]="false"
        [showEmptyState]="true"
      ></se-table>
    </ng-container>

    <ng-template #differentPresenter>
      <se-switch
        [id]="'substitutiveSwitchId'"
        [label]="
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.DIFFERENT_PRESENTER.SWITCH'
            | translate
        "
        formControlName="substitutiva"
        [tooltip]="true"
        [tooltipText]="
          'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.DIFFERENT_PRESENTER.TOOLTIP'
            | translate
        "
        (onToggle)="openDifferentPresenterForm($event)"
      >
      </se-switch>
      <ng-container *ngIf="showDifferentPresenterForm">
        <hr />
        <p>
          {{
            'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.DIFFERENT_PRESENTER.DESCRIPTION'
              | translate
          }}
        </p>
        <div class="row">
          <se-input
            class="col-12 col-md-3"
            formControlName="numJustificant"
            [label]="
              'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.TABLE.HEAD.JUSTIFICANT'
                | translate
            "
            [type]="'text'"
            [id]="'numJustificantDifferentPresenterId'"
          ></se-input>

          <se-datepicker
            class="col-12 col-md-3"
            [id]="'dataPresentacioDifferentPresenterId'"
            [label]="
              'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.TABLE.HEAD.DATA'
                | translate
            "
            formControlName="dataPresentacio"
            [placeholder]="'dd/mm/aaaa'"
          ></se-datepicker>
          <se-button
            class="col-12 col-md-3 align-self-center"
            [btnTheme]="'secondary'"
            [disabled]="isButtonDisabled()"
            (onClick)="validateDifferentPresenterData()"
          >
            {{ 'UI_COMPONENTS.BUTTONS.VALIDATE' | translate }}
          </se-button>
        </div>
        <se-alert
          *ngIf="showValidateResultMessage"
          [type]="showValidateResultMessage.type"
          [title]="showValidateResultMessage.message"
          [closeButton]="true"
        >
        </se-alert>
      </ng-container>
    </ng-template>
  </form>
</se-panel>
<!-- TEMPLATES -->
<ng-template #downloadDocuments>
  <ng-container
    *ngIf="_substitutiveData?.substitutive && _substitutiveData?.samePresenter"
  >
    <mf-documents-docs-actions
      *axLazyElement
      [documentsIds]="_substitutiveData?.substitutive?.numJustificant"
      [customFileName]="_substitutiveData?.substitutive?.numJustificant"
    />
  </ng-container>
</ng-template>
<!--/ TEMPLATES -->
